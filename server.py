from flask import Flask, render_template, jsonify
import requests
import socket
import re
import json

app = Flask(__name__)

def get_local_ip():
    """获取本机IPv4地址"""
    try:
        # 连接到一个远程地址来获取本地IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def fetch_online_records():
    """请求在线记录数据"""
    url = "https://xha.ouc.edu.cn:802/eportal/portal/page/loadOnlineRecord"
    params = {
        'callback': 'dr1004',
        'lang': 'zh-CN',
        'program_index': 'ctshNw1713845951',
        'page_index': 'V5fmKw1713845966',
        'user_account': '***********',
        'wlan_user_ip': '0.0.0.0',
        'wlan_user_mac': '************',
        'start_time': '2010-01-01',
        'end_time': '2100-01-01',
        'start_rn': '1',
        'end_rn': '5',
        'jsVersion': '4.1',
        'v': '6206'
    }
    
    try:
        response = requests.get(url, params=params, verify=False, timeout=10)
        # 解析JSONP响应
        jsonp_data = response.text
        # 提取JSON部分
        json_match = re.search(r'dr1004\((.*)\);?', jsonp_data)
        if json_match:
            json_data = json.loads(json_match.group(1))
            return json_data
        return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

@app.route('/')
def index():
    local_ip = get_local_ip()
    online_data = fetch_online_records()
    return render_template('index.html', local_ip=local_ip, data=online_data)

@app.route('/api/refresh')
def refresh_data():
    """刷新数据的API接口"""
    online_data = fetch_online_records()
    return jsonify(online_data)

if __name__ == '__main__':
    local_ip = get_local_ip()
    print(f"服务器启动在: http://{local_ip}:23333")
    app.run(host='0.0.0.0', port=23333, debug=True)