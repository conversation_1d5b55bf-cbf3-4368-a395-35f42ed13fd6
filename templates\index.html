<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线记录查询</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .info-box {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-online {
            color: #28a745;
            font-weight: bold;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            margin-right: 10px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
        .unbind-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .unbind-btn:hover {
            background: #c82333;
        }
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>在线记录查询系统</h1>
        </div>
        
        <div class="info-box">
            <strong>当前服务器IP:</strong> {{ local_ip }}:23333
        </div>
        
        <button class="refresh-btn" onclick="refreshData()">刷新数据</button>
        <button class="unbind-btn" onclick="unbindLogin()">下线登录</button>
        
        <div id="content">
            {% if data %}
                {% if data.code == 1 %}
                    <h3>{{ data.msg }} (共{{ data.count }}条记录)</h3>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>上线时间</th>
                                <th>源IP</th>
                                <th>源IPv6</th>
                                <th>MAC地址</th>
                                <th>BRAS-IP</th>
                                <th>是否当前IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in data.records %}
                            <tr>
                                <td>{{ record.login_time }}</td>
                                <td>{{ record.login_ip }}</td>
                                <td>{{ record.login_ip_v6 }}</td>
                                <td>{{ record.mac_address }}</td>
                                <td>{{ record.bas_ip }}</td>
                                <td>
                                    {% if record.is_owner_ip == "1" %}
                                        <span class="status-online">当前IP</span>
                                    {% else %}
                                        历史IP
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="error">
                        错误: {{ data.msg }}
                    </div>
                {% endif %}
            {% else %}
                <div class="error">
                    无法获取数据，请检查网络连接或稍后重试
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function refreshData() {
            fetch('/api/refresh')
                .then(response => response.json())
                .then(data => {
                    location.reload();
                })
                .catch(error => {
                    alert('刷新失败: ' + error);
                });
        }

        function unbindLogin() {
            if (confirm('确定要下线登录吗？')) {
                fetch('/api/unbind')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('下线成功: ' + data.message);
                            // 刷新页面以更新状态
                            location.reload();
                        } else {
                            alert('下线失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        alert('下线请求失败: ' + error);
                    });
            }
        }
    </script>
</body>
</html>